import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { ProdutoDto } from './dto/produto.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { ProdutoIntegrationService } from './services/produto-integration.service';

@Injectable()
export class ProdutoService {
  constructor(
    private readonly produtoIntegrationService: ProdutoIntegrationService,
  ) {}

  async create(createProdutoDto: CreateProdutoDto): Promise<ProdutoDto> {
    return await this.produtoIntegrationService.createProduto(createProdutoDto);
  }

  async findAll(
    paginationDto: PaginationQueryDto,
    referencia?: string,
  ): Promise<PaginatedResponseDto<ProdutoDto>> {
    const { page, limit } = paginationDto;

    if (referencia) {
      try {
        const produto = await this.produtoIntegrationService.findProdutoByReferencia(referencia);
        return {
          data: [produto],
          meta: {
            total: 1,
            page,
            limit,
            totalPages: 1,
          },
        };
      } catch (error) {
        return {
          data: [],
          meta: {
            total: 0,
            page,
            limit,
            totalPages: 0,
          },
        };
      }
    }

    // Buscar todos os produtos
    const produtos = await this.produtoIntegrationService.findAllProdutos();

    // Aplicar paginação manual
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProdutos = produtos.slice(startIndex, endIndex);

    return {
      data: paginatedProdutos,
      meta: {
        total: produtos.length,
        page,
        limit,
        totalPages: Math.ceil(produtos.length / limit),
      },
    };
  }

  async findByReferencia(referencia: string): Promise<ProdutoDto> {
    return await this.produtoIntegrationService.findProdutoByReferencia(referencia);
  }

  // Método temporário para compatibilidade com outros módulos
  // TODO: Remover quando todos os módulos forem migrados
  async findOne(id: number): Promise<ProdutoDto> {
    throw new Error(`Busca por ID não suportada na integração. ID: ${id}. Use busca por referência.`);
  }
}

