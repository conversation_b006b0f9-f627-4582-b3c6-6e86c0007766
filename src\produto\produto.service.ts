import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Produto } from './entities/produto.entity';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { ProdutoDto } from './dto/produto.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@Injectable()
export class ProdutoService {
  constructor(
    @InjectRepository(Produto)
    private produtoRepository: Repository<Produto>,
  ) {}

  async create(createProdutoDto: CreateProdutoDto): Promise<ProdutoDto> {
    const produto = this.produtoRepository.create(createProdutoDto);
    const savedProduto = await this.produtoRepository.save(produto);
    
    // Mapear para o DTO
    const produtoDto = new ProdutoDto();
    Object.assign(produtoDto, savedProduto);
    
    return produtoDto;
  }

  async findAll(
    paginationDto: PaginationQueryDto,
    nome?: string,
    codigo?: string,
  ): Promise<PaginatedResponseDto<ProdutoDto>> {
    const { page, limit } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: any = {};
    if (nome) {
      whereClause.nome = Like(`%${nome}%`);
    }
    if (codigo) {
      whereClause.codigo = codigo;
    }

    const [produtos, total] = await this.produtoRepository.findAndCount({
      where: whereClause,
      skip,
      take: limit,
      order: { id: 'DESC' },
    });

    // Mapear para o DTO
    const produtosDto = produtos.map(produto => {
      const produtoDto = new ProdutoDto();
      Object.assign(produtoDto, produto);
      return produtoDto;
    });

    return {
      data: produtosDto,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<ProdutoDto> {
    const produto = await this.produtoRepository.findOne({
      where: { id },
    });

    if (!produto) {
      throw new NotFoundException(`Produto com ID ${id} não encontrado`);
    }

    // Mapear para o DTO
    const produtoDto = new ProdutoDto();
    Object.assign(produtoDto, produto);
    
    return produtoDto;
  }
}

