import { Injectable } from '@nestjs/common';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { ProdutoDto } from './dto/produto.dto';
import { ProdutoIntegrationService } from './services/produto-integration.service';

@Injectable()
export class ProdutoService {
  constructor(
    private readonly produtoIntegrationService: ProdutoIntegrationService,
  ) {}

  async create(createProdutoDto: CreateProdutoDto): Promise<ProdutoDto> {
    return await this.produtoIntegrationService.createProduto(createProdutoDto);
  }

  async findAll(referencia?: string): Promise<ProdutoDto[]> {
    if (referencia) {
      try {
        const produto = await this.produtoIntegrationService.findProdutoByReferencia(referencia);
        return [produto];
      } catch (error) {
        return [];
      }
    }

    return await this.produtoIntegrationService.findAllProdutos();
  }

  async findByReferencia(referencia: string): Promise<ProdutoDto> {
    return await this.produtoIntegrationService.findProdutoByReferencia(referencia);
  }

  // Método temporário para compatibilidade com outros módulos
  async findOne(id: number): Promise<ProdutoDto> {
    throw new Error(`Busca por ID não suportada na integração. ID: ${id}. Use busca por referência.`);
  }
}

