import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateProdutoDto {
  @ApiProperty({ example: 'Smartphone XYZ', description: 'Nome do produto' })
  @IsNotEmpty()
  @IsString()
  nome: string;

  @ApiProperty({ example: 'PROD-123', description: 'Código do produto' })
  @IsNotEmpty()
  @IsString()
  codigo: string;

  @ApiProperty({ example: 'Smartphone com 128GB de armazenamento', description: 'Descrição do produto' })
  @IsOptional()
  @IsString()
  descricao: string;
}

