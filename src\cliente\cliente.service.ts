import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Cliente } from './entities/cliente.entity';
import { CreateClienteDto } from './dto/create-cliente.dto';
import { ClienteDto } from './dto/cliente.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@Injectable()
export class ClienteService {
  constructor(
    @InjectRepository(Cliente)
    private clienteRepository: Repository<Cliente>,
  ) {}

  async create(createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    const cliente = this.clienteRepository.create(createClienteDto);
    return await this.clienteRepository.save(cliente);
  }

  async findAll(
    paginationDto: PaginationQueryDto,
    cpf?: string,
    cnpj?: string,
  ): Promise<PaginatedResponseDto<ClienteDto>> {
    const { page, limit } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: any = {};
    if (cpf) {
      whereClause.cpf = cpf;
    }
    if (cnpj) {
      whereClause.cnpj = cnpj;
    }

    const [clientes, total] = await this.clienteRepository.findAndCount({
      where: whereClause,
      skip,
      take: limit,
      order: { id: 'DESC' },
    });

    // Mapear para o DTO
    const clientesDto = clientes.map(cliente => {
      const clienteDto = new ClienteDto();
      Object.assign(clienteDto, cliente);
      return clienteDto;
    });

    return {
      data: clientesDto,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<ClienteDto> {
    const cliente = await this.clienteRepository.findOne({ where: { id } });
    if (!cliente) {
      throw new NotFoundException(`Cliente com ID ${id} não encontrado`);
    }
    return cliente;
  }
}
