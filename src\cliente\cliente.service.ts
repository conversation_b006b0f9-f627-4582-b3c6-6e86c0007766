import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateClienteDto } from './dto/create-cliente.dto';
import { UpdateClienteDto } from './dto/update-cliente.dto';
import { ClienteDto } from './dto/cliente.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { ClienteIntegrationService } from './services/cliente-integration.service';

@Injectable()
export class ClienteService {
  constructor(
    private readonly clienteIntegrationService: ClienteIntegrationService,
  ) {}

  async create(createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    return await this.clienteIntegrationService.createCliente(createClienteDto);
  }

  async update(cpf: string, updateClienteDto: UpdateClienteDto): Promise<ClienteDto> {
    return await this.clienteIntegrationService.updateCliente(cpf, updateClienteDto);
  }


  async findOne(id: number): Promise<ClienteDto> {
    // Para a integração, vamos buscar por ID que na verdade é o código do cliente
    // Isso pode precisar ser ajustado dependendo de como você quer implementar
    throw new NotFoundException(`Busca por ID não implementada na integração. Use busca por CPF.`);
  }

  async findByCpf(cpf: string): Promise<ClienteDto> {
    return await this.clienteIntegrationService.findClienteByCpf(cpf);
  }
}
