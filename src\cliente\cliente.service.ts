import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateClienteDto } from './dto/create-cliente.dto';
import { UpdateClienteDto } from './dto/update-cliente.dto';
import { ClienteDto } from './dto/cliente.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { ClienteIntegrationService } from './services/cliente-integration.service';

@Injectable()
export class ClienteService {
  constructor(
    private readonly clienteIntegrationService: ClienteIntegrationService,
  ) {}

  async create(createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    return await this.clienteIntegrationService.createCliente(createClienteDto);
  }

  async update(cpf: string, updateClienteDto: UpdateClienteDto): Promise<ClienteDto> {
    return await this.clienteIntegrationService.updateCliente(cpf, updateClienteDto);
  }

  async findAll(
    paginationDto: PaginationQueryDto,
    cpf?: string,
    cnpj?: string,
  ): Promise<PaginatedResponseDto<ClienteDto>> {
    // Para a integração, vamos simular uma busca paginada
    // Na prática, você pode implementar uma busca mais sofisticada
    // ou manter um cache local se necessário

    if (cpf) {
      try {
        const cliente = await this.clienteIntegrationService.findClienteByCpf(cpf);
        return {
          data: [cliente],
          meta: {
            total: 1,
            page: paginationDto.page,
            limit: paginationDto.limit,
            totalPages: 1,
          },
        };
      } catch (error) {
        // Se não encontrar, retorna lista vazia
        return {
          data: [],
          meta: {
            total: 0,
            page: paginationDto.page,
            limit: paginationDto.limit,
            totalPages: 0,
          },
        };
      }
    }

    // Para busca sem filtros, retorna lista vazia por enquanto
    // Você pode implementar uma estratégia diferente se necessário
    return {
      data: [],
      meta: {
        total: 0,
        page: paginationDto.page,
        limit: paginationDto.limit,
        totalPages: 0,
      },
    };
  }

  async findOne(id: number): Promise<ClienteDto> {
    // Para a integração, vamos buscar por ID que na verdade é o código do cliente
    // Isso pode precisar ser ajustado dependendo de como você quer implementar
    throw new NotFoundException(`Busca por ID não implementada na integração. Use busca por CPF.`);
  }

  async findByCpf(cpf: string): Promise<ClienteDto> {
    return await this.clienteIntegrationService.findClienteByCpf(cpf);
  }
}
