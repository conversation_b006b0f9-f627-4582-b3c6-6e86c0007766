import { Injectable } from '@nestjs/common';
import { CreateClienteDto } from './dto/create-cliente.dto';
import { UpdateClienteDto } from './dto/update-cliente.dto';
import { ClienteDto } from './dto/cliente.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { ClienteIntegrationService } from './services/cliente-integration.service';

@Injectable()
export class ClienteService {
  constructor(
    private readonly clienteIntegrationService: ClienteIntegrationService,
  ) {}

  async create(createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    return await this.clienteIntegrationService.createCliente(createClienteDto);
  }

  async update(cpf: string, updateClienteDto: UpdateClienteDto): Promise<ClienteDto> {
    return await this.clienteIntegrationService.updateCliente(cpf, updateClienteDto);
  }




  async findByCpf(cpf: string): Promise<ClienteDto> {
    return await this.clienteIntegrationService.findClienteByCpf(cpf);
  }

  // Método temporário para compatibilidade com outros módulos
  // TODO: Remover quando todos os módulos forem migrados
  async findOne(id: number): Promise<ClienteDto> {
    throw new Error(`Busca por ID não suportada na integração. ID: ${id}. Use busca por CPF.`);
  }
}
