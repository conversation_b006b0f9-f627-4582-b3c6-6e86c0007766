import { ApiProperty } from '@nestjs/swagger';
import { ClienteDto } from '../../cliente/dto/cliente.dto';
import { AssistenciaDto } from '../../assistencia/dto/assistencia.dto';
import { ProdutoDto } from '../../produto/dto/produto.dto';
import { StatusDto } from './status.dto';

export class ProtocoloDto {
  @ApiProperty({ example: 1, description: 'ID do protocolo' })
  id: number;

  @ApiProperty({ type: ClienteDto, description: 'Cliente associado ao protocolo' })
  cliente: ClienteDto;

  @ApiProperty({ type: AssistenciaDto, description: 'Assistência técnica associada ao protocolo' })
  assistencia: AssistenciaDto;

  @ApiProperty({ type: ProdutoDto, description: 'Produto associado ao protocolo' })
  produto: ProdutoDto;

  @ApiProperty({ example: '2023-07-15T14:30:00Z', description: 'Data de criação do protocolo' })
  createdAt: string;

  @ApiProperty({ example: '2023-07-16T10:15:00Z', description: 'Data de atualização do protocolo' })
  updatedAt: string;

  @ApiProperty({ 
    example: 'Aberto', 
    description: 'Status atual do protocolo',
    enum: ['Aberto', 'Reparo', 'Troca', 'Reembolso', 'Finalizado', 'Cancelado']
  })
  status: string;

  @ApiProperty({ example: 'Produto não liga', description: 'Descrição do problema' })
  descricao: string;

  @ApiProperty({ type: [StatusDto], description: 'Histórico de status do protocolo' })
  historicoStatus: StatusDto[];
}