import { ApiProperty } from '@nestjs/swagger';

export class AssistenciaDto {
  @ApiProperty({ example: 1, description: 'ID da assistência técnica' })
  id: number;

  @ApiProperty({ example: 'Assistência Técnica ABC', description: 'Nome da assistência' })
  nome: string;

  @ApiProperty({ example: '12.345.678/0001-90', description: 'CNPJ da assistência' })
  cnpj: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email da assistência' })
  email: string;

  @ApiProperty({ example: '(11) 3456-7890', description: 'Telefone da assistência' })
  telefone: string;

  @ApiProperty({ example: '01234-567', description: 'CEP' })
  cep: string;

  @ApiProperty({ example: '123', description: 'Número do endereço' })
  numero: string;

  @ApiProperty({ example: 'Sala 101', description: 'Complemento do endereço', required: false })
  complemento?: string;

  @ApiProperty({ example: 'Centro', description: 'Bairro' })
  bairro: string;

  @ApiProperty({ example: 'São Paulo', description: 'Cidade' })
  cidade: string;

  @ApiProperty({ example: 'SP', description: 'Estado (UF)' })
  uf: string;

  @ApiProperty({ example: 5.2, description: 'Distância em km' })
  distancia: number;
}