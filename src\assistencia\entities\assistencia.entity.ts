import { <PERSON>tity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { Protocolo } from '../../protocolo/entities/protocolo.entity';

@Entity()
export class Assistencia {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  nome: string;

  @Column()
  cnpj: string;

  @Column()
  email: string;

  @Column()
  telefone: string;

  @Column()
  cep: string;

  @Column()
  numero: string;

  @Column({ nullable: true })
  complemento: string;

  @Column()
  bairro: string;

  @Column()
  cidade: string;

  @Column()
  uf: string;

  @OneToMany(() => Protocolo, protocolo => protocolo.assistencia)
  protocolos: Protocolo[];
}