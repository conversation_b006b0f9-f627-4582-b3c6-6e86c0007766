import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { IntegrationConfigService } from '../../common/services/integration-config.service';
import { CreateProdutoDto } from '../dto/create-produto.dto';
import { ProdutoDto } from '../dto/produto.dto';
import { ExternalProdutoRequestDto, ExternalProdutoResponseDto } from '../dto/external-api.dto';

@Injectable()
export class ProdutoIntegrationService {
  private readonly logger = new Logger(ProdutoIntegrationService.name);

  constructor(private readonly configService: IntegrationConfigService) {}

  /**
   * Cria um produto na API externa
   */
  async createProduto(createProdutoDto: CreateProdutoDto): Promise<ProdutoDto> {
    try {
      const externalDto = this.mapToExternalRequest(createProdutoDto);
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Criando produto na API externa: ${createProdutoDto.referencia}`);

      const response: AxiosResponse<ExternalProdutoResponseDto> = await axios.post(
        `${baseUrl}/produtos`,
        externalDto,
        { headers }
      );

      return this.mapToProdutoDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao criar produto: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao criar produto');
    }
  }

  /**
   * Busca todos os produtos na API externa
   */
  async findAllProdutos(): Promise<ProdutoDto[]> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log('Buscando todos os produtos na API externa');

      const response: AxiosResponse<ExternalProdutoResponseDto[]> = await axios.get(
        `${baseUrl}/produtos`,
        { headers }
      );

      return response.data.map(produto => this.mapToProdutoDto(produto));
    } catch (error) {
      this.logger.error(`Erro ao buscar produtos: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar produtos');
    }
  }

  /**
   * Busca um produto por referência na API externa
   */
  async findProdutoByReferencia(referencia: string): Promise<ProdutoDto> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Buscando produto na API externa: referência ${referencia}`);

      const response: AxiosResponse<ExternalProdutoResponseDto> = await axios.get(
        `${baseUrl}/produtos/referencia/${referencia}`,
        { headers }
      );

      return this.mapToProdutoDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao buscar produto: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar produto');
    }
  }

  /**
   * Mapeia CreateProdutoDto para o formato da API externa
   */
  private mapToExternalRequest(dto: CreateProdutoDto): ExternalProdutoRequestDto {
    return {
      referencia: dto.referencia,
      descricao: dto.descricao,
      codigoFamilia: dto.codigoFamilia,
      codigoLinha: dto.codigoLinha,
      garantia: dto.garantia,
      maoDeObra: dto.maoDeObra || '0',
      maoDeObraAdmin: dto.maoDeObraAdmin || '0',
      numeroSerieObrigatorio: dto.numeroSerieObrigatorio ?? true,
      ativo: dto.ativo ?? true,
    };
  }

  /**
   * Mapeia resposta da API externa para ProdutoDto
   */
  private mapToProdutoDto(externalDto: ExternalProdutoResponseDto): ProdutoDto {
    const produtoDto = new ProdutoDto();
    produtoDto.id = externalDto.produto;
    produtoDto.referencia = externalDto.referencia;
    produtoDto.descricao = externalDto.descricao;
    produtoDto.garantia = externalDto.garantia;
    produtoDto.ativo = externalDto.ativo === 1;
    produtoDto.familia = externalDto.familia.descricao;
    produtoDto.linha = externalDto.linha.nome;
    produtoDto.voltagem = externalDto.voltagem || undefined;
    produtoDto.origem = externalDto.origem;

    return produtoDto;
  }

  /**
   * Trata erros da API externa
   */
  private handleApiError(error: any, message: string): never {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 404) {
        throw new HttpException('Produto não encontrado', HttpStatus.NOT_FOUND);
      }
      
      throw new HttpException(
        `${message}: ${data?.message || error.message}`,
        status >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
      );
    }
    
    throw new HttpException(
      `${message}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}
