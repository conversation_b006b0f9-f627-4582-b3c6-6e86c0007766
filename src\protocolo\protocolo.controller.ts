import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ProtocoloService } from './protocolo.service';
import { CreateProtocoloDto } from './dto/create-protocolo.dto';
import { ProtocoloDto } from './dto/protocolo.dto';
import { ProtocoloListDto } from './dto/protocolo-list.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@ApiTags('protocolos')
@Controller('protocolos')
export class ProtocoloController {
  constructor(private readonly protocoloService: ProtocoloService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo protocolo' })
  @ApiResponse({ status: 201, description: 'Protocolo criado com sucesso', type: ProtocoloDto })
  create(@Body() createProtocoloDto: CreateProtocoloDto): Promise<ProtocoloDto> {
    return this.protocoloService.create(createProtocoloDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os protocolos com paginação e filtros' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página' })
  @ApiQuery({ name: 'clienteId', required: false, description: 'Filtrar por ID do cliente' })
  @ApiQuery({ name: 'status', required: false, description: 'Filtrar por status (PENDENTE/ABERTO/RESOLVIDO)' })
  @ApiQuery({ name: 'numero', required: false, description: 'Filtrar por número do protocolo' })
  @ApiResponse({ status: 200, description: 'Lista de protocolos retornada com sucesso' })
  findAll(
    @Query() paginationDto: PaginationQueryDto,
    @Query('clienteId') clienteId?: number,
    @Query('status') status?: string,
    @Query('numero') numero?: string,
  ): Promise<PaginatedResponseDto<ProtocoloListDto>> {
    return this.protocoloService.findAll(paginationDto, clienteId, status, numero);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um protocolo pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do protocolo' })
  @ApiResponse({ status: 200, description: 'Protocolo encontrado', type: ProtocoloDto })
  @ApiResponse({ status: 404, description: 'Protocolo não encontrado' })
  findOne(@Param('id') id: string): Promise<ProtocoloDto> {
    return this.protocoloService.findOne(+id);
  }
}
