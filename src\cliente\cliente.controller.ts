import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ClienteService } from './cliente.service';
import { CreateClienteDto } from './dto/create-cliente.dto';
import { ClienteDto } from './dto/cliente.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@ApiTags('clientes')
@Controller('clientes')
export class ClienteController {
  constructor(private readonly clienteService: ClienteService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo cliente' })
  @ApiResponse({ status: 201, description: 'Cliente criado com sucesso', type: ClienteDto })
  create(@Body() createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    return this.clienteService.create(createClienteDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os clientes com paginação e filtros' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página' })
  @ApiQuery({ name: 'cpf', required: false, description: 'Filtrar por CPF' })
  @ApiQuery({ name: 'cnpj', required: false, description: 'Filtrar por CNPJ' })
  @ApiQuery({ name: 'cpfCnpj', required: false, description: 'Filtrar por CPF ou CNPJ' })
  @ApiResponse({ status: 200, description: 'Lista de clientes retornada com sucesso' })
  findAll(
    @Query() paginationDto: PaginationQueryDto,
    @Query('cpf') cpf?: string,
    @Query('cnpj') cnpj?: string,
    @Query('cpfCnpj') cpfCnpj?: string,
  ): Promise<PaginatedResponseDto<ClienteDto>> {
    // Se cpfCnpj for fornecido, verificar se é CPF ou CNPJ
    if (cpfCnpj) {
      if (cpfCnpj.length <= 11) {
        cpf = cpfCnpj;
      } else {
        cnpj = cpfCnpj;
      }
    }
    return this.clienteService.findAll(paginationDto, cpf, cnpj);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um cliente pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do cliente' })
  @ApiResponse({ status: 200, description: 'Cliente encontrado', type: ClienteDto })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado' })
  findOne(@Param('id') id: string): Promise<ClienteDto> {
    return this.clienteService.findOne(+id);
  }
}
