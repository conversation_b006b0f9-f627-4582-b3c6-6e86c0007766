import { Controller, Get, Post, Put, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ClienteService } from './cliente.service';
import { CreateClienteDto } from './dto/create-cliente.dto';
import { UpdateClienteDto } from './dto/update-cliente.dto';
import { ClienteDto } from './dto/cliente.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@ApiTags('clientes')
@Controller('clientes')
export class ClienteController {
  constructor(private readonly clienteService: ClienteService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo cliente' })
  @ApiResponse({ status: 201, description: 'Cliente criado com sucesso', type: ClienteDto })
  create(@Body() createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    return this.clienteService.create(createClienteDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os clientes com paginação e filtros' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página' })
  @ApiQuery({ name: 'cpf', required: false, description: 'Filtrar por CPF' })
  @ApiQuery({ name: 'cnpj', required: false, description: 'Filtrar por CNPJ' })
  @ApiQuery({ name: 'cpfCnpj', required: false, description: 'Filtrar por CPF ou CNPJ' })
  @ApiResponse({ status: 200, description: 'Lista de clientes retornada com sucesso' })
  findAll(
    @Query() paginationDto: PaginationQueryDto,
    @Query('cpf') cpf?: string,
    @Query('cnpj') cnpj?: string,
    @Query('cpfCnpj') cpfCnpj?: string,
  ): Promise<PaginatedResponseDto<ClienteDto>> {
    // Se cpfCnpj for fornecido, verificar se é CPF ou CNPJ
    if (cpfCnpj) {
      if (cpfCnpj.length <= 11) {
        cpf = cpfCnpj;
      } else {
        cnpj = cpfCnpj;
      }
    }
    return this.clienteService.findAll(paginationDto, cpf, cnpj);
  }

  @Put('cpf/:cpf')
  @ApiOperation({ summary: 'Atualizar um cliente pelo CPF' })
  @ApiParam({ name: 'cpf', description: 'CPF do cliente (apenas números)' })
  @ApiResponse({ status: 200, description: 'Cliente atualizado com sucesso', type: ClienteDto })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado' })
  update(@Param('cpf') cpf: string, @Body() updateClienteDto: UpdateClienteDto): Promise<ClienteDto> {
    return this.clienteService.update(cpf, updateClienteDto);
  }

  @Get('cpf/:cpf')
  @ApiOperation({ summary: 'Buscar um cliente pelo CPF' })
  @ApiParam({ name: 'cpf', description: 'CPF do cliente (apenas números)' })
  @ApiResponse({ status: 200, description: 'Cliente encontrado', type: ClienteDto })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado' })
  findByCpf(@Param('cpf') cpf: string): Promise<ClienteDto> {
    return this.clienteService.findByCpf(cpf);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um cliente pelo ID (DEPRECATED - Use busca por CPF)' })
  @ApiParam({ name: 'id', description: 'ID do cliente' })
  @ApiResponse({ status: 200, description: 'Cliente encontrado', type: ClienteDto })
  @ApiResponse({ status: 404, description: 'Cliente não encontrado' })
  findOne(@Param('id') id: string): Promise<ClienteDto> {
    return this.clienteService.findOne(+id);
  }
}
