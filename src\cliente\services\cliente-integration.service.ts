import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { IntegrationConfigService } from '../../common/services/integration-config.service';
import { CreateClienteDto } from '../dto/create-cliente.dto';
import { UpdateClienteDto } from '../dto/update-cliente.dto';
import { ClienteDto } from '../dto/cliente.dto';
import { ExternalClienteRequestDto, ExternalClienteResponseDto } from '../dto/external-api.dto';

@Injectable()
export class ClienteIntegrationService {
  private readonly logger = new Logger(ClienteIntegrationService.name);

  constructor(private readonly configService: IntegrationConfigService) {}

  /**
   * Cria um cliente na API externa
   */
  async createCliente(createClienteDto: CreateClienteDto): Promise<ClienteDto> {
    try {
      const externalDto = this.mapToExternalRequest(createClienteDto);
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Criando cliente na API externa: ${createClienteDto.nome}`);

      const response: AxiosResponse<ExternalClienteResponseDto> = await axios.post(
        `${baseUrl}/clientes`,
        externalDto,
        { headers }
      );

      return this.mapToClienteDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao criar cliente: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao criar cliente');
    }
  }

  /**
   * Atualiza um cliente na API externa
   */
  async updateCliente(cpf: string, updateClienteDto: UpdateClienteDto): Promise<ClienteDto> {
    try {
      const externalDto = this.mapUpdateToExternalRequest(updateClienteDto);
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Atualizando cliente na API externa: CPF ${cpf}`);

      const response: AxiosResponse<ExternalClienteResponseDto> = await axios.put(
        `${baseUrl}/clientes/cpf/${cpf}`,
        externalDto,
        { headers }
      );

      return this.mapToClienteDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao atualizar cliente: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao atualizar cliente');
    }
  }

  /**
   * Busca um cliente por CPF na API externa
   */
  async findClienteByCpf(cpf: string): Promise<ClienteDto> {
    try {
      const headers = this.configService.getIntegrationHeaders();
      const baseUrl = this.configService.getBaseUrl();

      this.logger.log(`Buscando cliente na API externa: CPF ${cpf}`);

      const response: AxiosResponse<ExternalClienteResponseDto> = await axios.get(
        `${baseUrl}/clientes/cpf/${cpf}`,
        { headers }
      );

      return this.mapToClienteDto(response.data);
    } catch (error) {
      this.logger.error(`Erro ao buscar cliente: ${error.message}`, error.stack);
      this.handleApiError(error, 'Erro ao buscar cliente');
    }
  }

  /**
   * Mapeia CreateClienteDto para o formato da API externa
   */
  private mapToExternalRequest(dto: CreateClienteDto): ExternalClienteRequestDto {
    return {
      nome: dto.nome,
      cpf: this.cleanCpf(dto.cpf),
      cep: this.cleanCep(dto.cep),
      cidade: dto.cidade,
      estado: dto.uf,
      endereco: `${dto.bairro}`, // Pode ser ajustado conforme necessário
      complemento: dto.complemento || '',
      numero: dto.numero,
      fone: this.cleanPhone(dto.telefone),
      email: dto.email,
      codigoCliente: this.generateClientCode(), // Gerar código único
    };
  }

  /**
   * Mapeia UpdateClienteDto para o formato da API externa
   */
  private mapUpdateToExternalRequest(dto: UpdateClienteDto): ExternalClienteRequestDto {
    return {
      nome: dto.nome,
      cpf: this.cleanCpf(dto.cpf),
      cep: this.cleanCep(dto.cep),
      cidade: dto.cidade,
      estado: dto.uf,
      endereco: `${dto.bairro}`, // Pode ser ajustado conforme necessário
      complemento: dto.complemento || '',
      numero: dto.numero,
      fone: this.cleanPhone(dto.telefone),
      email: dto.email,
      codigoCliente: dto.codigoCliente,
    };
  }

  /**
   * Mapeia resposta da API externa para ClienteDto
   */
  private mapToClienteDto(externalDto: ExternalClienteResponseDto): ClienteDto {
    const clienteDto = new ClienteDto();
    clienteDto.id = parseInt(externalDto.codigo_cliente); // Usando código como ID
    clienteDto.nome = externalDto.nome;
    clienteDto.cpf = this.formatCpf(externalDto.cpf);
    clienteDto.email = externalDto.email;
    clienteDto.telefone = this.formatPhone(externalDto.fone);
    clienteDto.cep = this.formatCep(externalDto.cep);
    clienteDto.numero = externalDto.numero;
    clienteDto.complemento = '';
    clienteDto.bairro = ''; // A API externa não retorna bairro separadamente
    clienteDto.cidade = ''; // A API externa não retorna cidade separadamente
    clienteDto.uf = externalDto.estado;

    return clienteDto;
  }

  /**
   * Remove formatação do CPF
   */
  private cleanCpf(cpf: string): string {
    return cpf?.replace(/\D/g, '') || '';
  }

  /**
   * Remove formatação do CEP
   */
  private cleanCep(cep: string): string {
    return cep?.replace(/\D/g, '') || '';
  }

  /**
   * Remove formatação do telefone
   */
  private cleanPhone(phone: string): string {
    return phone?.replace(/\D/g, '') || '';
  }

  /**
   * Formata CPF para exibição
   */
  private formatCpf(cpf: string): string {
    if (!cpf || cpf.length !== 11) return cpf;
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }

  /**
   * Formata CEP para exibição
   */
  private formatCep(cep: string): string {
    if (!cep || cep.length !== 8) return cep;
    return cep.replace(/(\d{5})(\d{3})/, '$1-$2');
  }

  /**
   * Formata telefone para exibição
   */
  private formatPhone(phone: string): string {
    if (!phone) return phone;
    if (phone.length === 11) {
      return phone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    }
    if (phone.length === 10) {
      return phone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return phone;
  }

  /**
   * Gera um código único para o cliente
   */
  private generateClientCode(): string {
    return Date.now().toString();
  }

  /**
   * Trata erros da API externa
   */
  private handleApiError(error: any, message: string): never {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      if (status === 404) {
        throw new HttpException('Cliente não encontrado', HttpStatus.NOT_FOUND);
      }
      
      throw new HttpException(
        `${message}: ${data?.message || error.message}`,
        status >= 500 ? HttpStatus.INTERNAL_SERVER_ERROR : HttpStatus.BAD_REQUEST
      );
    }
    
    throw new HttpException(
      `${message}: ${error.message}`,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}
