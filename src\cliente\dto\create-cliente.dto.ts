import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateClienteDto {
  @ApiProperty({ example: '<PERSON>', description: 'Nome do cliente' })
  @IsNotEmpty()
  @IsString()
  nome: string;

  @ApiProperty({ example: '123.456.789-00', description: 'CPF do cliente' })
  @IsNotEmpty()
  @IsString()
  cpf: string;

  @ApiProperty({ example: '12.345.678/0001-90', description: 'CNPJ do cliente', required: false })
  @IsOptional()
  @IsString()
  cnpj?: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email do cliente' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ example: '(11) 98765-4321', description: 'Telefone do cliente' })
  @IsNotEmpty()
  @IsString()
  telefone: string;

  @ApiProperty({ example: '01234-567', description: 'CEP' })
  @IsNotEmpty()
  @IsString()
  cep: string;

  @ApiProperty({ example: '123', description: 'Número do endereço' })
  @IsNotEmpty()
  @IsString()
  numero: string;

  @ApiProperty({ example: 'Apto 101', description: 'Complemento do endereço', required: false })
  @IsOptional()
  @IsString()
  complemento?: string;

  @ApiProperty({ example: 'Centro', description: 'Bairro' })
  @IsNotEmpty()
  @IsString()
  bairro: string;

  @ApiProperty({ example: 'São Paulo', description: 'Cidade' })
  @IsNotEmpty()
  @IsString()
  cidade: string;

  @ApiProperty({ example: 'SP', description: 'Estado (UF)' })
  @IsNotEmpty()
  @IsString()
  uf: string;
}
