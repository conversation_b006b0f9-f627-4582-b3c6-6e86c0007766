import { ApiProperty } from '@nestjs/swagger';

export class ProdutoDto {
  @ApiProperty({ example: 1, description: 'ID do produto' })
  id: number;

  @ApiProperty({ example: 'Smartphone XYZ', description: 'Nome do produto' })
  nome: string;

  @ApiProperty({ example: 'PROD-123', description: 'Código do produto' })
  codigo: string;

  @ApiProperty({ example: 'Smartphone com 128GB de armazenamento', description: 'Descrição do produto' })
  descricao: string;
}

