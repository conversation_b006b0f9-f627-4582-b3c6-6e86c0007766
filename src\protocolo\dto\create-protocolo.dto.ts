import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreateProtocoloDto {
  @ApiProperty({ example: 1, description: 'ID do cliente' })
  @IsNotEmpty()
  @IsNumber()
  clienteId: number;

  @ApiProperty({ example: 1, description: 'ID do produto' })
  @IsNotEmpty()
  @IsNumber()
  produtoId: number;

  @ApiProperty({ example: 'NF123456', description: 'Número da nota fiscal' })
  @IsNotEmpty()
  @IsString()
  nf: string;

  @ApiProperty({ example: '2023-07-10', description: 'Data da compra' })
  @IsNotEmpty()
  @IsString()
  data: string;

  @ApiProperty({ example: 'Produto não liga após queda', description: 'Descrição do problema' })
  @IsNotEmpty()
  @IsString()
  descricao: string;
}