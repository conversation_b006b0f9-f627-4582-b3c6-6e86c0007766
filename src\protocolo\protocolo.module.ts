import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProtocoloController } from './protocolo.controller';
import { ProtocoloService } from './protocolo.service';
import { Protocolo } from './entities/protocolo.entity';
import { Status } from './entities/status.entity';
import { ClienteModule } from '../cliente/cliente.module';
import { ProdutoModule } from '../produto/produto.module';
import { AssistenciaModule } from '../assistencia/assistencia.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Protocolo, Status]),
    ClienteModule,
    ProdutoModule,
    AssistenciaModule,
  ],
  controllers: [ProtocoloController],
  providers: [ProtocoloService],
})
export class ProtocoloModule {}