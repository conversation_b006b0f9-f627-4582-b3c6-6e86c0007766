{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/clientes": {"post": {"operationId": "ClienteController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClienteDto"}}}}, "responses": {"201": {"description": "Cliente criado com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClienteDto"}}}}}, "summary": "Criar um novo cliente", "tags": ["clientes"]}, "get": {"operationId": "ClienteController_findAll", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Número da página", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "<PERSON>ite de itens por página", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "cpf", "required": false, "in": "query", "description": "Filtrar por CPF", "schema": {"type": "string"}}, {"name": "cnpj", "required": false, "in": "query", "description": "Filtrar por CNPJ", "schema": {"type": "string"}}, {"name": "cpfCnpj", "required": false, "in": "query", "description": "Filtrar por CPF ou CNPJ", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de clientes retornada com sucesso"}}, "summary": "Listar todos os clientes com paginação e filtros", "tags": ["clientes"]}}, "/clientes/{id}": {"get": {"operationId": "ClienteController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID do cliente", "schema": {"type": "string"}}], "responses": {"200": {"description": "Cliente encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClienteDto"}}}}, "404": {"description": "Cliente não encontrado"}}, "summary": "Buscar um cliente pelo ID", "tags": ["clientes"]}}, "/produtos": {"post": {"operationId": "ProdutoController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProdutoDto"}}}}, "responses": {"201": {"description": "Produto criado com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoDto"}}}}}, "summary": "Criar um novo produto", "tags": ["produtos"]}, "get": {"operationId": "ProdutoController_findAll", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Número da página", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "<PERSON>ite de itens por página", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "nome", "required": false, "in": "query", "description": "Filtrar por nome do produto", "schema": {"type": "string"}}, {"name": "codigo", "required": false, "in": "query", "description": "Filtrar por código do produto", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de produtos retornada com sucesso"}}, "summary": "Listar todos os produtos com paginação e filtros", "tags": ["produtos"]}}, "/produtos/{id}": {"get": {"operationId": "ProdutoController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID do produto", "schema": {"type": "string"}}], "responses": {"200": {"description": "Produto encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoDto"}}}}, "404": {"description": "Produto não encontrado"}}, "summary": "Buscar um produto pelo ID", "tags": ["produtos"]}}, "/assistencias/localizar": {"get": {"operationId": "AssistenciaController_findNearby", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Número da página", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "<PERSON>ite de itens por página", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "cep", "required": true, "in": "query", "description": "CEP para busca de proximidade", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de assistências técnicas retornada com sucesso"}}, "summary": "Localizar assistências técnicas próximas por CEP", "tags": ["assistencias"]}}, "/protocolos": {"post": {"operationId": "ProtocoloController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProtocoloDto"}}}}, "responses": {"201": {"description": "Protocolo criado com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProtocoloDto"}}}}}, "summary": "Criar um novo protocolo", "tags": ["protocolos"]}, "get": {"operationId": "ProtocoloController_findAll", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Número da página", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "<PERSON>ite de itens por página", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "clienteId", "required": false, "in": "query", "description": "Filtrar por ID do cliente", "schema": {"type": "number"}}, {"name": "status", "required": false, "in": "query", "description": "Filtrar por status (PENDENTE/ABERTO/RESOLVIDO)", "schema": {"type": "string"}}, {"name": "numero", "required": false, "in": "query", "description": "Filtrar por número do protocolo", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de protocolos retornada com sucesso"}}, "summary": "Listar todos os protocolos com paginação e filtros", "tags": ["protocolos"]}}, "/protocolos/{id}": {"get": {"operationId": "ProtocoloController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID do protocolo", "schema": {"type": "string"}}], "responses": {"200": {"description": "Protocolo encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProtocoloDto"}}}}, "404": {"description": "Protocolo não encontrado"}}, "summary": "Buscar um protocolo pelo ID", "tags": ["protocolos"]}}, "/notas_fiscais": {"post": {"operationId": "NotaFiscalController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotaFiscalDto"}}}}, "responses": {"201": {"description": "Nota fiscal criada com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotaFiscalDto"}}}}}, "summary": "Criar uma nova nota fiscal", "tags": ["notas_fiscais"]}, "get": {"operationId": "NotaFiscalController_findAll", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Número da página", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "<PERSON>ite de itens por página", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "numero", "required": false, "in": "query", "description": "Filtrar por número da nota fiscal", "schema": {"type": "string"}}, {"name": "cpfCnpj", "required": false, "in": "query", "description": "Filtrar por CPF/CNPJ do cliente", "schema": {"type": "string"}}, {"name": "clienteId", "required": false, "in": "query", "description": "Filtrar por ID do cliente", "schema": {"type": "number"}}], "responses": {"200": {"description": "Lista de notas fiscais retornada com sucesso"}}, "summary": "Listar todas as notas fiscais com paginação e filtros", "tags": ["notas_fiscais"]}}, "/notas_fiscais/{id}/produtos": {"post": {"operationId": "NotaFiscalController_adicionarProduto", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID da nota fiscal", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotaFiscalProdutoDto"}}}}, "responses": {"201": {"description": "Produto adicionado com sucesso"}}, "summary": "Adicionar produto a uma nota fiscal existente", "tags": ["notas_fiscais"]}}, "/notas_fiscais/{id}/produtos/{produtoId}": {"delete": {"operationId": "NotaFiscalController_removerProduto", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID da nota fiscal", "schema": {"type": "string"}}, {"name": "produtoId", "required": true, "in": "path", "description": "ID do relacionamento nota fiscal-produto", "schema": {"type": "string"}}], "responses": {"200": {"description": "Produto removido com sucesso"}}, "summary": "Remover produto de uma nota fiscal", "tags": ["notas_fiscais"]}}, "/notas_fiscais/{id}": {"get": {"operationId": "NotaFiscalController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID da nota fiscal", "schema": {"type": "string"}}], "responses": {"200": {"description": "Nota fiscal encontrada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotaFiscalDto"}}}}, "404": {"description": "Nota fiscal não encontrada"}}, "summary": "Buscar uma nota fiscal pelo ID", "tags": ["notas_fiscais"]}}, "/gestaoweb/obter_coleta": {"get": {"operationId": "GestaowebController_getCollect", "parameters": [{"name": "protocoloOrdemServico", "required": true, "in": "query", "description": "Filtrar por numero do protocolo ou ordem de serviço", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "description": "Filtrar por status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Gestaoweb"]}}, "/gestaoweb/obter_entrega": {"get": {"operationId": "GestaowebController_getDelivery", "parameters": [{"name": "protocoloOrdemServico", "required": true, "in": "query", "description": "Filtrar por numero do protocolo ou ordem de serviço", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "description": "Filtrar por status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Gestaoweb"]}}}, "info": {"title": "Base API", "description": "API base com NestJS e Swagger", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"CreateClienteDto": {"type": "object", "properties": {"nome": {"type": "string", "example": "<PERSON>", "description": "Nome do cliente"}, "cpf": {"type": "string", "example": "123.456.789-00", "description": "CPF do cliente"}, "cnpj": {"type": "string", "example": "12.345.678/0001-90", "description": "CNPJ do cliente"}, "email": {"type": "string", "example": "<EMAIL>", "description": "Email do cliente"}, "telefone": {"type": "string", "example": "(11) 98765-4321", "description": "Telefone do cliente"}, "cep": {"type": "string", "example": "01234-567", "description": "CEP"}, "numero": {"type": "string", "example": "123", "description": "Número do endereço"}, "complemento": {"type": "string", "example": "Apto 101", "description": "Complemento do endereço"}, "bairro": {"type": "string", "example": "Centro", "description": "Bairro"}, "cidade": {"type": "string", "example": "São Paulo", "description": "Cidade"}, "uf": {"type": "string", "example": "SP", "description": "Estado (UF)"}}, "required": ["nome", "email", "telefone", "cep", "numero", "bairro", "cidade", "uf"]}, "ClienteDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID do cliente"}, "nome": {"type": "string", "example": "<PERSON>", "description": "Nome do cliente"}, "cpf": {"type": "string", "example": "123.456.789-00", "description": "CPF do cliente"}, "cnpj": {"type": "string", "example": "12.345.678/0001-90", "description": "CNPJ do cliente"}, "email": {"type": "string", "example": "<EMAIL>", "description": "Email do cliente"}, "telefone": {"type": "string", "example": "(11) 98765-4321", "description": "Telefone do cliente"}, "cep": {"type": "string", "example": "01234-567", "description": "CEP"}, "numero": {"type": "string", "example": "123", "description": "Número do endereço"}, "complemento": {"type": "string", "example": "Apto 101", "description": "Complemento do endereço"}, "bairro": {"type": "string", "example": "Centro", "description": "Bairro"}, "cidade": {"type": "string", "example": "São Paulo", "description": "Cidade"}, "uf": {"type": "string", "example": "SP", "description": "Estado (UF)"}}, "required": ["id", "nome", "email", "telefone", "cep", "numero", "bairro", "cidade", "uf"]}, "CreateProdutoDto": {"type": "object", "properties": {"nome": {"type": "string", "example": "Smartphone XYZ", "description": "Nome do produto"}, "codigo": {"type": "string", "example": "PROD-123", "description": "Código do produto"}, "descricao": {"type": "string", "example": "Smartphone com 128GB de armazenamento", "description": "Descrição do produto"}}, "required": ["nome", "codigo", "descricao"]}, "ProdutoDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID do produto"}, "nome": {"type": "string", "example": "Smartphone XYZ", "description": "Nome do produto"}, "codigo": {"type": "string", "example": "PROD-123", "description": "Código do produto"}, "descricao": {"type": "string", "example": "Smartphone com 128GB de armazenamento", "description": "Descrição do produto"}}, "required": ["id", "nome", "codigo", "descricao"]}, "CreateProtocoloDto": {"type": "object", "properties": {"clienteId": {"type": "number", "example": 1, "description": "ID do cliente"}, "produtoId": {"type": "number", "example": 1, "description": "ID do produto"}, "nf": {"type": "string", "example": "NF123456", "description": "Número da nota fiscal"}, "data": {"type": "string", "example": "2023-07-10", "description": "Data da compra"}, "descricao": {"type": "string", "example": "Produto não liga após queda", "description": "Descrição do problema"}}, "required": ["clienteId", "produtoId", "nf", "data", "descricao"]}, "AssistenciaDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID da assistência técnica"}, "nome": {"type": "string", "example": "Assistência Técnica ABC", "description": "Nome da assistência"}, "cnpj": {"type": "string", "example": "12.345.678/0001-90", "description": "CNPJ da assistê<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>", "description": "<PERSON><PERSON>"}, "telefone": {"type": "string", "example": "(11) 3456-7890", "description": "Telefone da assistência"}, "cep": {"type": "string", "example": "01234-567", "description": "CEP"}, "numero": {"type": "string", "example": "123", "description": "Número do endereço"}, "complemento": {"type": "string", "example": "Sala 101", "description": "Complemento do endereço"}, "bairro": {"type": "string", "example": "Centro", "description": "Bairro"}, "cidade": {"type": "string", "example": "São Paulo", "description": "Cidade"}, "uf": {"type": "string", "example": "SP", "description": "Estado (UF)"}, "distancia": {"type": "number", "example": 5.2, "description": "Distância em km"}}, "required": ["id", "nome", "cnpj", "email", "telefone", "cep", "numero", "bairro", "cidade", "uf", "distancia"]}, "StatusDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID do status"}, "status": {"type": "string", "example": "Abe<PERSON>o", "description": "Status do protocolo", "enum": ["Abe<PERSON>o", "Reparo", "Troca", "Reembolso", "Finalizado", "Cancelado"]}, "data": {"type": "string", "example": "2023-07-15", "description": "Data do status"}, "hora": {"type": "string", "example": "14:30", "description": "Hora do status"}, "observacao": {"type": "string", "example": "Aguardando aná<PERSON> t<PERSON>", "description": "Observação sobre o status"}}, "required": ["id", "status", "data", "hora", "observacao"]}, "ProtocoloDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID do protocolo"}, "cliente": {"description": "Cliente associado ao protocolo", "allOf": [{"$ref": "#/components/schemas/ClienteDto"}]}, "assistencia": {"description": "Assistência técnica associada ao protocolo", "allOf": [{"$ref": "#/components/schemas/AssistenciaDto"}]}, "produto": {"description": "Produto associado ao protocolo", "allOf": [{"$ref": "#/components/schemas/ProdutoDto"}]}, "createdAt": {"type": "string", "example": "2023-07-15T14:30:00Z", "description": "Data de criação do protocolo"}, "updatedAt": {"type": "string", "example": "2023-07-16T10:15:00Z", "description": "Data de atualização do protocolo"}, "status": {"type": "string", "example": "Abe<PERSON>o", "description": "Status atual do protocolo", "enum": ["Abe<PERSON>o", "Reparo", "Troca", "Reembolso", "Finalizado", "Cancelado"]}, "descricao": {"type": "string", "example": "Produto não liga", "description": "Descrição do problema"}, "historicoStatus": {"description": "Histórico de status do protocolo", "type": "array", "items": {"$ref": "#/components/schemas/StatusDto"}}}, "required": ["id", "cliente", "assistencia", "produto", "createdAt", "updatedAt", "status", "descricao", "historicoStatus"]}, "CreateNotaFiscalProdutoDto": {"type": "object", "properties": {"produtoId": {"type": "number", "example": 1, "description": "ID do produto"}, "quantidade": {"type": "number", "example": 2, "description": "Quantidade do produto"}, "valorUnitario": {"type": "number", "example": 599.99, "description": "Valor unitário do produto"}}, "required": ["produtoId", "quantidade", "valorUnitario"]}, "CreateNotaFiscalDto": {"type": "object", "properties": {"numero": {"type": "string", "example": "NF123456", "description": "Número da nota fiscal"}, "dataEmissao": {"type": "string", "example": "2023-07-10", "description": "Data de emissão"}, "clienteId": {"type": "number", "example": 1, "description": "ID do cliente"}, "produtos": {"description": "Lista de produtos da nota fiscal", "type": "array", "items": {"$ref": "#/components/schemas/CreateNotaFiscalProdutoDto"}}}, "required": ["numero", "dataEmissao", "clienteId", "produtos"]}, "NotaFiscalProdutoDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID do relacionamento"}, "produto": {"description": "Produto associado à nota fiscal", "allOf": [{"$ref": "#/components/schemas/ProdutoDto"}]}, "quantidade": {"type": "number", "example": 2, "description": "Quantidade do produto"}, "valorUnitario": {"type": "number", "example": 599.99, "description": "Valor unitário do produto"}, "valorTotal": {"type": "number", "example": 1199.98, "description": "Valor total (quantidade * valorUnitario)"}}, "required": ["id", "produto", "quantidade", "valorUnitario", "valorTotal"]}, "NotaFiscalDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID da nota fiscal"}, "numero": {"type": "string", "example": "NF123456", "description": "Número da nota fiscal"}, "valor": {"type": "number", "example": 1299.99, "description": "Valor total da nota fiscal"}, "dataEmissao": {"format": "date-time", "type": "string", "example": "2023-07-10", "description": "Data de emissão"}, "cliente": {"description": "Cliente associado à nota fiscal", "allOf": [{"$ref": "#/components/schemas/ClienteDto"}]}, "notaFiscalProdutos": {"description": "Produtos da nota fiscal", "type": "array", "items": {"$ref": "#/components/schemas/NotaFiscalProdutoDto"}}, "createdAt": {"format": "date-time", "type": "string", "example": "2023-07-15T14:30:00Z", "description": "Data de criação do registro"}}, "required": ["id", "numero", "valor", "dataEmissao", "cliente", "notaFiscalP<PERSON><PERSON>s", "createdAt"]}}}}