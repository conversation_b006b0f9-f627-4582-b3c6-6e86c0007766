import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ProdutoService } from './produto.service';
import { CreateProdutoDto } from './dto/create-produto.dto';
import { ProdutoDto } from './dto/produto.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@ApiTags('produtos')
@Controller('produtos')
export class ProdutoController {
  constructor(private readonly produtoService: ProdutoService) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo produto' })
  @ApiResponse({ status: 201, description: 'Produto criado com sucesso', type: ProdutoDto })
  create(@Body() createProdutoDto: CreateProdutoDto): Promise<ProdutoDto> {
    return this.produtoService.create(createProdutoDto);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os produtos com paginação e filtros' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página' })
  @ApiQuery({ name: 'nome', required: false, description: 'Filtrar por nome do produto' })
  @ApiQuery({ name: 'codigo', required: false, description: 'Filtrar por código do produto' })
  @ApiResponse({ status: 200, description: 'Lista de produtos retornada com sucesso' })
  findAll(
    @Query() paginationDto: PaginationQueryDto,
    @Query('nome') nome?: string,
    @Query('codigo') codigo?: string,
  ): Promise<PaginatedResponseDto<ProdutoDto>> {
    return this.produtoService.findAll(paginationDto, nome, codigo);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um produto pelo ID' })
  @ApiParam({ name: 'id', description: 'ID do produto' })
  @ApiResponse({ status: 200, description: 'Produto encontrado', type: ProdutoDto })
  @ApiResponse({ status: 404, description: 'Produto não encontrado' })
  findOne(@Param('id') id: string): Promise<ProdutoDto> {
    return this.produtoService.findOne(+id);
  }
}

