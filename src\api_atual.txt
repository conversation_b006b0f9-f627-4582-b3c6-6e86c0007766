{"openapi": "3.0.0", "paths": {"/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["App"]}}, "/clientes": {"post": {"operationId": "ClienteController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClienteDto"}}}}, "responses": {"201": {"description": "Cliente criado com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClienteDto"}}}}}, "summary": "Criar um novo cliente", "tags": ["clientes"]}}, "/clientes/{cpf}": {"put": {"operationId": "ClienteController_update", "parameters": [{"name": "cpf", "required": true, "in": "path", "description": "CPF do cliente (apenas números)", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateClienteDto"}}}}, "responses": {"200": {"description": "Cliente atualizado com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClienteDto"}}}}, "404": {"description": "Cliente não encontrado"}}, "summary": "Atualizar um cliente pelo CPF", "tags": ["clientes"]}, "get": {"operationId": "ClienteController_findByCpf", "parameters": [{"name": "cpf", "required": true, "in": "path", "description": "CPF do cliente (apenas números)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Cliente encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClienteDto"}}}}, "404": {"description": "Cliente não encontrado"}}, "summary": "Buscar um cliente pelo CPF", "tags": ["clientes"]}}, "/produtos": {"post": {"operationId": "ProdutoController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProdutoDto"}}}}, "responses": {"201": {"description": "Produto criado com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoDto"}}}}}, "summary": "Criar um novo produto", "tags": ["produtos"]}, "get": {"operationId": "ProdutoController_findAll", "parameters": [{"name": "referencia", "required": false, "in": "query", "description": "Filtrar por referência do produto", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de produtos retornada com sucesso"}}, "summary": "Listar todos os produtos com filtros", "tags": ["produtos"]}}, "/produtos/{referencia}": {"get": {"operationId": "ProdutoController_findByReferencia", "parameters": [{"name": "referencia", "required": true, "in": "path", "description": "Referência do produto", "schema": {"type": "string"}}], "responses": {"200": {"description": "Produto encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProdutoDto"}}}}, "404": {"description": "Produto não encontrado"}}, "summary": "Buscar um produto pela referência", "tags": ["produtos"]}}, "/assistencias/localizar": {"get": {"operationId": "AssistenciaController_findNearby", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Número da página", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "<PERSON>ite de itens por página", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "cep", "required": true, "in": "query", "description": "CEP para busca de proximidade", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de assistências técnicas retornada com sucesso"}}, "summary": "Localizar assistências técnicas próximas por CEP", "tags": ["assistencias"]}}, "/protocolos": {"post": {"operationId": "ProtocoloController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProtocoloDto"}}}}, "responses": {"201": {"description": "Protocolo criado com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProtocoloDto"}}}}}, "summary": "Criar um novo protocolo", "tags": ["protocolos"]}, "get": {"operationId": "ProtocoloController_findByPeriod", "parameters": [{"name": "dataInicial", "required": true, "in": "query", "description": "Data inicial (yyyy-MM-dd)", "schema": {"type": "string"}}, {"name": "dataFinal", "required": true, "in": "query", "description": "Data final (yyyy-MM-dd)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de protocolos retornada com sucesso"}}, "summary": "Buscar protocolos por período", "tags": ["protocolos"]}}, "/protocolos/cpf/{cpf}": {"get": {"operationId": "ProtocoloController_findByCpf", "parameters": [{"name": "cpf", "required": true, "in": "path", "description": "CPF do cliente (apenas números)", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista de protocolos retornada com sucesso"}}, "summary": "Buscar protocolos por CPF", "tags": ["protocolos"]}}, "/protocolos/{id}": {"get": {"operationId": "ProtocoloController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID do protocolo", "schema": {"type": "string"}}], "responses": {"200": {"description": "Protocolo encontrado", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProtocoloDto"}}}}, "404": {"description": "Protocolo não encontrado"}}, "summary": "Buscar um protocolo pelo ID", "tags": ["protocolos"]}}, "/notas_fiscais": {"post": {"operationId": "NotaFiscalController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotaFiscalDto"}}}}, "responses": {"201": {"description": "Nota fiscal criada com sucesso", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotaFiscalDto"}}}}}, "summary": "Criar uma nova nota fiscal", "tags": ["notas_fiscais"]}, "get": {"operationId": "NotaFiscalController_findAll", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Número da página", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "<PERSON>ite de itens por página", "schema": {"default": 10, "example": 10, "type": "number"}}, {"name": "numero", "required": false, "in": "query", "description": "Filtrar por número da nota fiscal", "schema": {"type": "string"}}, {"name": "cpfCnpj", "required": false, "in": "query", "description": "Filtrar por CPF/CNPJ do cliente", "schema": {"type": "string"}}, {"name": "clienteId", "required": false, "in": "query", "description": "Filtrar por ID do cliente", "schema": {"type": "number"}}], "responses": {"200": {"description": "Lista de notas fiscais retornada com sucesso"}}, "summary": "Listar todas as notas fiscais com paginação e filtros", "tags": ["notas_fiscais"]}}, "/notas_fiscais/{id}/produtos": {"post": {"operationId": "NotaFiscalController_adicionarProduto", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID da nota fiscal", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNotaFiscalProdutoDto"}}}}, "responses": {"201": {"description": "Produto adicionado com sucesso"}}, "summary": "Adicionar produto a uma nota fiscal existente", "tags": ["notas_fiscais"]}}, "/notas_fiscais/{id}/produtos/{produtoId}": {"delete": {"operationId": "NotaFiscalController_removerProduto", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID da nota fiscal", "schema": {"type": "string"}}, {"name": "produtoId", "required": true, "in": "path", "description": "ID do relacionamento nota fiscal-produto", "schema": {"type": "string"}}], "responses": {"200": {"description": "Produto removido com sucesso"}}, "summary": "Remover produto de uma nota fiscal", "tags": ["notas_fiscais"]}}, "/notas_fiscais/{id}": {"get": {"operationId": "NotaFiscalController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "ID da nota fiscal", "schema": {"type": "string"}}], "responses": {"200": {"description": "Nota fiscal encontrada", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotaFiscalDto"}}}}, "404": {"description": "Nota fiscal não encontrada"}}, "summary": "Buscar uma nota fiscal pelo ID", "tags": ["notas_fiscais"]}}, "/gestaoweb/obter_coleta": {"get": {"operationId": "GestaowebController_getCollect", "parameters": [{"name": "protocoloOrdemServico", "required": true, "in": "query", "description": "Filtrar por numero do protocolo ou ordem de serviço", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "description": "Filtrar por status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Gestaoweb"]}}, "/gestaoweb/obter_entrega": {"get": {"operationId": "GestaowebController_getDelivery", "parameters": [{"name": "protocoloOrdemServico", "required": true, "in": "query", "description": "Filtrar por numero do protocolo ou ordem de serviço", "schema": {"type": "string"}}, {"name": "status", "required": true, "in": "query", "description": "Filtrar por status", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Gestaoweb"]}}}, "info": {"title": "Base API", "description": "API base com NestJS e Swagger", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"CreateClienteDto": {"type": "object", "properties": {"nome": {"type": "string", "example": "<PERSON>", "description": "Nome do cliente"}, "cpf": {"type": "string", "example": "123.456.789-00", "description": "CPF do cliente"}, "email": {"type": "string", "example": "<EMAIL>", "description": "Email do cliente"}, "telefone": {"type": "string", "example": "(11) 98765-4321", "description": "Telefone do cliente"}, "cep": {"type": "string", "example": "01234-567", "description": "CEP"}, "numero": {"type": "string", "example": "123", "description": "Número do endereço"}, "complemento": {"type": "string", "example": "Apto 101", "description": "Complemento do endereço"}, "bairro": {"type": "string", "example": "Centro", "description": "Bairro"}, "cidade": {"type": "string", "example": "São Paulo", "description": "Cidade"}, "uf": {"type": "string", "example": "SP", "description": "Estado (UF)"}}, "required": ["nome", "cpf", "email", "telefone", "cep", "numero", "bairro", "cidade", "uf"]}, "ClienteDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID do cliente"}, "nome": {"type": "string", "example": "<PERSON>", "description": "Nome do cliente"}, "cpf": {"type": "string", "example": "123.456.789-00", "description": "CPF do cliente"}, "email": {"type": "string", "example": "<EMAIL>", "description": "Email do cliente"}, "telefone": {"type": "string", "example": "(11) 98765-4321", "description": "Telefone do cliente"}, "cep": {"type": "string", "example": "01234-567", "description": "CEP"}, "numero": {"type": "string", "example": "123", "description": "Número do endereço"}, "complemento": {"type": "string", "example": "Apto 101", "description": "Complemento do endereço"}, "bairro": {"type": "string", "example": "Centro", "description": "Bairro"}, "cidade": {"type": "string", "example": "São Paulo", "description": "Cidade"}, "uf": {"type": "string", "example": "SP", "description": "Estado (UF)"}}, "required": ["id", "nome", "email", "telefone", "cep", "numero", "bairro", "cidade", "uf"]}, "UpdateClienteDto": {"type": "object", "properties": {"nome": {"type": "string", "example": "<PERSON>", "description": "Nome do cliente"}, "cpf": {"type": "string", "example": "123.456.789-00", "description": "CPF do cliente"}, "email": {"type": "string", "example": "<EMAIL>", "description": "Email do cliente"}, "telefone": {"type": "string", "example": "(11) 98765-4321", "description": "Telefone do cliente"}, "cep": {"type": "string", "example": "01234-567", "description": "CEP"}, "numero": {"type": "string", "example": "123", "description": "Número do endereço"}, "complemento": {"type": "string", "example": "Apto 101", "description": "Complemento do endereço"}, "bairro": {"type": "string", "example": "Centro", "description": "Bairro"}, "cidade": {"type": "string", "example": "São Paulo", "description": "Cidade"}, "uf": {"type": "string", "example": "SP", "description": "Estado (UF)"}, "codigoCliente": {"type": "string", "example": "133131", "description": "Código do cliente"}}, "required": ["nome", "cpf", "email", "telefone", "cep", "numero", "bairro", "cidade", "uf", "codigoCliente"]}, "CreateProdutoDto": {"type": "object", "properties": {"referencia": {"type": "string", "example": "AM104INLIPTN", "description": "Referência do produto"}, "descricao": {"type": "string", "example": "AUTOCLAVE AMORA 04 INOX 127-220V LILAS PORTUGUES N", "description": "Descrição do produto"}, "codigoFamilia": {"type": "string", "example": "01", "description": "Código da família"}, "codigoLinha": {"type": "string", "example": "01", "description": "<PERSON><PERSON><PERSON> da l<PERSON>ha"}, "garantia": {"type": "string", "example": "12", "description": "Garantia em meses"}, "maoDeObra": {"type": "string", "example": "0", "description": "Mão de obra"}, "maoDeObraAdmin": {"type": "string", "example": "0", "description": "Mão de obra admin"}, "numeroSerieObrigatorio": {"type": "boolean", "example": true, "description": "Número de série obrigatório"}, "ativo": {"type": "boolean", "example": true, "description": "Produto ativo"}}, "required": ["referencia", "descricao", "codigoFamilia", "codigoLinha", "garantia"]}, "ProdutoDto": {"type": "object", "properties": {"id": {"type": "number", "example": 408790, "description": "ID do produto"}, "referencia": {"type": "string", "example": "AM104INLIPTN", "description": "Referência do produto"}, "descricao": {"type": "string", "example": "AUTOCLAVE AMORA 04 INOX 127-220V LILAS PORTUGUES N", "description": "Descrição do produto"}, "garantia": {"type": "number", "example": 24, "description": "Garantia em meses"}, "ativo": {"type": "boolean", "example": true, "description": "Produto ativo"}, "familia": {"type": "string", "example": "AUTOCLAVE AMORA", "description": "Descrição da família"}, "linha": {"type": "string", "example": "BIOSEGUERANÇA", "description": "<PERSON>me da linha"}, "voltagem": {"type": "string", "example": "BIVOLT", "description": "Voltagem do produto"}, "origem": {"type": "string", "example": "NAC", "description": "Origem do produto"}}, "required": ["id", "referencia", "descricao", "garantia", "ativo", "familia", "linha", "origem"]}, "CreateProtocoloAnexoDto": {"type": "object", "properties": {"descricao": {"type": "string", "example": "teste.png", "description": "Descrição do anexo"}, "arquivo": {"type": "string", "example": "iVBORw0KGgoAAAANSUhEUgAAAOEAAADhCAVORK5CYII=", "description": "Arquivo em base64"}}, "required": ["descricao", "arquivo"]}, "CreateProtocoloProdutoDto": {"type": "object", "properties": {"referencia": {"type": "string", "example": "6426-01", "description": "Referência do produto"}, "voltagem": {"type": "string", "example": "220V", "description": "Voltagem do produto"}, "nota_fiscal": {"type": "string", "example": "321654", "description": "Número da nota fiscal"}, "data_nf": {"type": "string", "example": "2020-01-05", "description": "Data da nota fiscal"}, "defeito_reclamado": {"type": "string", "example": "Não Liga", "description": "Defeito reclamado"}, "os": {"type": "string", "example": "54691217", "description": "Número da <PERSON>"}}, "required": ["referencia", "voltagem", "nota_fiscal", "data_nf", "defeito_reclamado", "os"]}, "CreateProtocoloDto": {"type": "object", "properties": {"nome": {"type": "string", "example": "<PERSON>", "description": "Nome do cliente"}, "cpf": {"type": "string", "example": "32156464000", "description": "CPF do cliente"}, "email": {"type": "string", "example": "<EMAIL>", "description": "Email do cliente"}, "celular": {"type": "string", "example": "99999999999", "description": "Celular do cliente"}, "fone": {"type": "string", "example": "99999999999", "description": "Telefone do cliente"}, "cep": {"type": "string", "example": "17519255", "description": "CEP"}, "endereco": {"type": "string", "example": "Rua A", "description": "Endereço"}, "numero": {"type": "string", "example": "100", "description": "Número do endereço"}, "origem": {"type": "string", "example": "CHAT", "description": "Origem do protocolo"}, "classificacao": {"type": "string", "example": "BACKOFFICE", "description": "Classificação do protocolo"}, "anexos": {"description": "Lista de anexos", "type": "array", "items": {"$ref": "#/components/schemas/CreateProtocoloAnexoDto"}}, "produtos": {"description": "Lista de produtos", "type": "array", "items": {"$ref": "#/components/schemas/CreateProtocoloProdutoDto"}}, "horario": {"type": "string", "example": "16:30", "description": "<PERSON><PERSON><PERSON><PERSON>"}}, "required": ["nome", "cpf", "email", "celular", "fone", "cep", "endereco", "numero", "origem", "classificacao", "produtos", "horario"]}, "ProtocoloProdutoDto": {"type": "object", "properties": {"referencia": {"type": "string", "example": "ABCD", "description": "Referência do produto"}, "descricao": {"type": "string", "example": "Produto 1", "description": "Descrição do produto"}, "qtde": {"type": "string", "example": "1", "description": "Quantidade"}, "serie": {"type": "string", "example": "54615646", "description": "Número de série"}, "defeito_reclamado": {"type": "string", "example": "Não aquece", "description": "Defeito reclamado"}, "posto": {"type": "string", "example": "PA001 - Posto indicado", "description": "Post<PERSON>"}, "os": {"type": "string", "example": "234567", "description": "Número da <PERSON>"}}, "required": ["referencia", "descricao", "qtde", "serie", "defeito_reclamado", "posto", "os"]}, "ProtocoloDto": {"type": "object", "properties": {"id": {"type": "number", "example": 12345, "description": "ID do protocolo"}, "protocolo": {"type": "number", "example": 12345, "description": "Número do protocolo"}, "dataAbertura": {"type": "string", "example": "2020-03-05", "description": "Data de abertura"}, "classificacao": {"type": "string", "example": "BACKOFFICE", "description": "Classificação do protocolo"}, "providencia": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "origem": {"type": "string", "example": "Telefone", "description": "Origem do protocolo"}, "situacao": {"type": "string", "example": "Abe<PERSON>o", "description": "Situação do protocolo"}, "produtos": {"description": "Lista de produtos do protocolo", "type": "array", "items": {"$ref": "#/components/schemas/ProtocoloProdutoDto"}}}, "required": ["id", "protocolo", "dataAbertura", "classificacao", "providencia", "origem", "situacao", "produtos"]}, "CreateNotaFiscalProdutoDto": {"type": "object", "properties": {"produtoId": {"type": "number", "example": 1, "description": "ID do produto"}, "quantidade": {"type": "number", "example": 2, "description": "Quantidade do produto"}, "valorUnitario": {"type": "number", "example": 599.99, "description": "Valor unitário do produto"}}, "required": ["produtoId", "quantidade", "valorUnitario"]}, "CreateNotaFiscalDto": {"type": "object", "properties": {"numero": {"type": "string", "example": "NF123456", "description": "Número da nota fiscal"}, "dataEmissao": {"type": "string", "example": "2023-07-10", "description": "Data de emissão"}, "clienteId": {"type": "number", "example": 1, "description": "ID do cliente"}, "produtos": {"description": "Lista de produtos da nota fiscal", "type": "array", "items": {"$ref": "#/components/schemas/CreateNotaFiscalProdutoDto"}}}, "required": ["numero", "dataEmissao", "clienteId", "produtos"]}, "NotaFiscalProdutoDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID do relacionamento"}, "produto": {"description": "Produto associado à nota fiscal", "allOf": [{"$ref": "#/components/schemas/ProdutoDto"}]}, "quantidade": {"type": "number", "example": 2, "description": "Quantidade do produto"}, "valorUnitario": {"type": "number", "example": 599.99, "description": "Valor unitário do produto"}, "valorTotal": {"type": "number", "example": 1199.98, "description": "Valor total (quantidade * valorUnitario)"}}, "required": ["id", "produto", "quantidade", "valorUnitario", "valorTotal"]}, "NotaFiscalDto": {"type": "object", "properties": {"id": {"type": "number", "example": 1, "description": "ID da nota fiscal"}, "numero": {"type": "string", "example": "NF123456", "description": "Número da nota fiscal"}, "valor": {"type": "number", "example": 1299.99, "description": "Valor total da nota fiscal"}, "dataEmissao": {"format": "date-time", "type": "string", "example": "2023-07-10", "description": "Data de emissão"}, "cliente": {"description": "Cliente associado à nota fiscal", "allOf": [{"$ref": "#/components/schemas/ClienteDto"}]}, "notaFiscalProdutos": {"description": "Produtos da nota fiscal", "type": "array", "items": {"$ref": "#/components/schemas/NotaFiscalProdutoDto"}}, "createdAt": {"format": "date-time", "type": "string", "example": "2023-07-15T14:30:00Z", "description": "Data de criação do registro"}}, "required": ["id", "numero", "valor", "dataEmissao", "cliente", "notaFiscalP<PERSON><PERSON>s", "createdAt"]}}}}