import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Protocolo } from './entities/protocolo.entity';
import { CreateProtocoloDto } from './dto/create-protocolo.dto';
import { ProtocoloDto } from './dto/protocolo.dto';
import { ProtocoloListDto } from './dto/protocolo-list.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { ClienteService } from '../cliente/cliente.service';
import { ProdutoService } from '../produto/produto.service';
import { AssistenciaService } from '../assistencia/assistencia.service';
import { Status } from './entities/status.entity';

@Injectable()
export class ProtocoloService {
  constructor(
    @InjectRepository(Protocolo)
    private protocoloRepository: Repository<Protocolo>,
    @InjectRepository(Status)
    private statusRepository: Repository<Status>,
    private clienteService: ClienteService,
    private produtoService: ProdutoService,
    private assistenciaService: AssistenciaService,
  ) {}

  async create(createProtocoloDto: CreateProtocoloDto): Promise<ProtocoloDto> {
    // Buscar cliente e produto
    const cliente = await this.clienteService.findOne(createProtocoloDto.clienteId);
    const produto = await this.produtoService.findOne(createProtocoloDto.produtoId);

    // Buscar assistência técnica mais próxima
    const assistencias = await this.assistenciaService.findNearby(
      { page: 1, limit: 1 },
      cliente.cep,
    );
    const assistencia = assistencias.data[0];

    // Criar protocolo
    const protocolo = this.protocoloRepository.create({
      cliente: { id: cliente.id },
      produto: { id: produto.id },
      assistencia: { id: assistencia.id },
      descricao: createProtocoloDto.descricao,
      nf: createProtocoloDto.nf,
      dataCompra: createProtocoloDto.data,
      status: 'Aberto',
    });

    const protocoloSalvo = await this.protocoloRepository.save(protocolo);

    // Criar status inicial
    const status = this.statusRepository.create({
      protocolo: { id: protocoloSalvo.id },
      status: 'Aberto',
      data: new Date().toISOString().split('T')[0],
      hora: new Date().toISOString().split('T')[1].substring(0, 5),
      observacao: 'Protocolo aberto',
    });

    await this.statusRepository.save(status);

    return this.findOne(protocoloSalvo.id);
  }

  async findAll(
    paginationDto: PaginationQueryDto,
    clienteId?: number,
    status?: string,
    numero?: string,
  ): Promise<PaginatedResponseDto<ProtocoloListDto>> {
    const { page, limit } = paginationDto;
    const skip = (page - 1) * limit;

    const whereClause: any = {};
    if (clienteId) {
      whereClause.cliente = { id: clienteId };
    }
    if (status) {
      whereClause.status = status;
    }
    if (numero) {
      whereClause.id = numero;
    }

    const [protocolos, total] = await this.protocoloRepository.findAndCount({
      where: whereClause,
      relations: ['cliente', 'produto', 'assistencia'],
      skip,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    // Mapear os resultados para o DTO
    const protocolosDto = protocolos.map(protocolo => {
      const protocoloListDto = new ProtocoloListDto();
      Object.assign(protocoloListDto, {
        id: protocolo.id,
        cliente: protocolo.cliente,
        assistencia: protocolo.assistencia,
        produto: protocolo.produto,
        createdAt: protocolo.createdAt,
        updatedAt: protocolo.updatedAt,
        status: protocolo.status,
        descricao: protocolo.descricao
      });
      return protocoloListDto;
    });

    return {
      data: protocolosDto,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<ProtocoloDto> {
    const protocolo = await this.protocoloRepository.findOne({
      where: { id },
      relations: ['cliente', 'produto', 'assistencia', 'historicoStatus'],
    });

    if (!protocolo) {
      throw new NotFoundException(`Protocolo com ID ${id} não encontrado`);
    }

    // Mapear para o DTO
    const protocoloDto = new ProtocoloDto();
    Object.assign(protocoloDto, {
      id: protocolo.id,
      cliente: protocolo.cliente,
      assistencia: protocolo.assistencia,
      produto: protocolo.produto,
      createdAt: protocolo.createdAt,
      updatedAt: protocolo.updatedAt,
      status: protocolo.status,
      descricao: protocolo.descricao,
      historicoStatus: protocolo.historicoStatus,
      nf: protocolo.nf,
      dataCompra: protocolo.dataCompra
    });

    return protocoloDto;
  }
}

